Feature: Multi-User Folder Permissions

  @e2e @multi-user @folder-permissions
  Scenario: Multi-user folder creation and access permissions
    Given The test environment is cleaned up
    When UserA creates three folders
    And UserC creates one folder
    Then UserA can see their own folders
    And UserC can see their own folder
    And UserB has view-only access
    And The test cleanup removes all created folders

  @e2e @multi-user @folder-permissions
  Scenario: Verify user permission restrictions
    Given The test environment is cleaned up
    When UserA creates three folders
    And UserC creates one folder
    Then UserA can see folderA1, folderA2, folderA3
    And UserA cannot see folderC
    And UserC can see folder<PERSON> only
    And UserC cannot see folderA1, folderA2, folderA3
    And UserB cannot create folders
    And UserB has view-only access to the system
    And The test cleanup removes all created folders
