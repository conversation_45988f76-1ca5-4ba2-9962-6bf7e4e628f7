Feature: Multi-User Folder Permissions

  @e2e @multi-user @folder-permissions
  Scenario: Multi-user folder creation and basic access
    Given The test environment is cleaned up
    When UserA creates three folders
    And UserB creates one folder
    Then UserA can see their own folders
    And UserB can see their own folder
    And UserC has limited access
    And The test cleanup removes all created folders

  @e2e @multi-user @folder-permissions @skip
  Scenario: Advanced folder sharing between users
    Given The test folders exist
    When UserA shares folderA2 with UserB
    And UserA shares folderA3 with all users
    And UserB shares folderB with UserC
    Then UserA can access their own folders and shared folders
    And UserB can access shared folders from UserA and their own folder
    And UserC can access shared folders only
    And Admin user can access all folders
