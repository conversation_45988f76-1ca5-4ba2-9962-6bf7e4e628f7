Feature: Multi-User Folder Permissions

  @e2e @multi-user @folder-permissions
  Scenario: Multi-user folder creation test
    Given The user deletes the following SDOs and folders if exist:
      | folderName     |
      | MultiUser Test A |
      | MultiUser Test C |
    When User<PERSON> creates a default case id "MultiUser Test A"
    Then The user sees notification "Case Created Successfully"
    And The user closes the success case modal
    When User3 attempts to create a case but fails
    When User2 has no create permissions
    Then User1 can see case "MultiUser Test A" in case table list
    And The user deletes the following SDOs and folders if exist:
      | folderName     |
      | MultiUser Test A |
      | MultiUser Test C |
