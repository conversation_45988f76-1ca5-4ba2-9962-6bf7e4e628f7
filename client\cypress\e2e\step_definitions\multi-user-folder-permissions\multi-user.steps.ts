import { Before, When, Then } from '@badeball/cypress-cucumber-preprocessor';
import { caseManagerPage } from '../../../pages/caseManagerPage';
import '../common/common.step';
import '../case-manager/case-manager.steps';

const users = {
  user1: 'user1',
  user2: 'user2',
  user3: 'user3',
};

Before({ tags: '@multi-user' }, () => {
  cy.LoginAndSwitchOrg(
    '71459e59-e555-4c3d-a391-9adb79a6576b',
    Cypress.env('username')
  );
});

When('User1 creates a default case id {string}', (caseId: string) => {
  cy.loginAsUser(users.user1);
  caseManagerPage.visit();
  caseManagerPage.createDefaultCase(caseId);
});

When('User3 attempts to create a case but fails', () => {
  cy.loginAsUser(users.user3);
  caseManagerPage.visit();

  caseManagerPage.clickAddButton();
  caseManagerPage.clickCreateNewCaseButton();

  cy.get('body').should('contain', 'Create New Case');

  caseManagerPage.typeCaseId('MultiUser Test C');
  caseManagerPage.typeCaseDescription('Test case for User3');
  caseManagerPage.clickCreateEditCaseButton('save');

  cy.log('User3 create attempt - may fail due to permissions');
});

When('User2 has no create permissions', () => {
  cy.loginAsUser(users.user2);
  caseManagerPage.visit();

  cy.getDataIdCy({ idAlias: 'AddIcon' }).should('not.exist');
  cy.log('User2 has no create permissions - Add button should not exist');
});

Then('User1 can see case {string} in case table list', (caseId: string) => {
  cy.loginAsUser(users.user1);
  caseManagerPage.visit();
  caseManagerPage.verifyCaseInTable(caseId);
});
