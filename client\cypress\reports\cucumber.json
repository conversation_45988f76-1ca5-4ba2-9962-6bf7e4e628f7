[{"description": "", "elements": [{"description": "", "id": "case-manager;filter-case-table", "keyword": "<PERSON><PERSON><PERSON>", "line": 160, "name": "Filter case table", "steps": [{"keyword": "Before", "hidden": true, "result": {"status": "passed", "duration": 2091000000}}, {"arguments": [], "keyword": "Given ", "line": 4, "name": "The user is on Case Management screen", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:15"}, "result": {"status": "passed", "duration": 17338000000}}, {"arguments": [], "keyword": "Given ", "line": 161, "name": "The user deletes the following cases if exist: \"Cypress Test Filtered\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:80"}, "result": {"status": "passed", "duration": 751000000}}, {"arguments": [], "keyword": "Given ", "line": 162, "name": "The user creates a default case id \"Cypress Test Filtered\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:47"}, "result": {"status": "passed", "duration": 8729000000}}, {"arguments": [], "keyword": "Then ", "line": 163, "name": "The user sees notification \"Case Created Successfully\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:206"}, "result": {"status": "passed", "duration": 10026000000}}, {"arguments": [], "keyword": "And ", "line": 164, "name": "The user closes the success case modal", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:56"}, "result": {"status": "passed", "duration": 175000000}}, {"arguments": [], "keyword": "When ", "line": 165, "name": "The user presses the filter button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:103"}, "result": {"status": "passed", "duration": 391000000}}, {"arguments": [], "keyword": "Then ", "line": 166, "name": "The filter drawer should be shown", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:227"}, "result": {"status": "passed", "duration": 5657000000}}, {"arguments": [], "keyword": "When ", "line": 167, "name": "The user enters \"Filtered\" in the case id filter", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:106"}, "result": {"status": "passed", "duration": 861000000}}, {"arguments": [], "keyword": "And ", "line": 168, "name": "The user clicks the \"Apply\" filter button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:109"}, "result": {"status": "passed", "duration": 1152000000}}, {"arguments": [], "keyword": "And ", "line": 169, "name": "The cases should be filtered by \"caseId\" \"Filtered\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:230"}, "result": {"status": "passed", "duration": 66000000}}, {"arguments": [], "keyword": "When ", "line": 170, "name": "The user presses the filter button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:103"}, "result": {"status": "passed", "duration": 254000000}}, {"arguments": [], "keyword": "And ", "line": 171, "name": "The user enters \"Cypress Test Filtered\" in the case id filter", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:106"}, "result": {"status": "passed", "duration": 2632000000}}, {"arguments": [], "keyword": "And ", "line": 172, "name": "The user clicks the \"Apply\" filter button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:109"}, "result": {"status": "passed", "duration": 1602000000}}, {"arguments": [], "keyword": "And ", "line": 173, "name": "The cases should be filtered by \"caseId\" \"Cypress Test Filtered\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:230"}, "result": {"status": "passed", "duration": 121000000}}, {"arguments": [], "keyword": "When ", "line": 174, "name": "The user presses the filter button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:103"}, "result": {"status": "passed", "duration": 194000000}}, {"arguments": [], "keyword": "And ", "line": 175, "name": "The user selects status \"open\" in the filter", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:113"}, "result": {"status": "passed", "duration": 11231000000}}, {"arguments": [], "keyword": "And ", "line": 176, "name": "The user clicks the \"Apply\" filter button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:109"}, "result": {"status": "passed", "duration": 1012000000}}, {"arguments": [], "keyword": "And ", "line": 177, "name": "The cases should be filtered by \"statusId\" \"open\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:230"}, "result": {"status": "passed", "duration": 129000000}}, {"arguments": [], "keyword": "When ", "line": 178, "name": "The user presses the filter button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:103"}, "result": {"status": "passed", "duration": 291000000}}, {"arguments": [], "keyword": "And ", "line": 179, "name": "The user selects tag \"Close1\" in the filter", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:116"}, "result": {"status": "passed", "duration": 5767000000}}, {"arguments": [], "keyword": "And ", "line": 180, "name": "The user clicks the \"Apply\" filter button", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:109"}, "result": {"status": "passed", "duration": 604000000}}, {"arguments": [], "keyword": "When ", "line": 181, "name": "The user opens case \"Cypress Test Filtered\"", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:119"}, "result": {"status": "passed", "duration": 203000000}}, {"arguments": [], "keyword": "Then ", "line": 182, "name": "The user sees \"Close1\" tag in details", "match": {"location": "webpack://investigate-client/cypress/e2e/step_definitions/case-manager/case-manager.steps.ts:233"}, "result": {"status": "passed", "duration": 1478000000}}], "tags": [{"name": "@e2e", "line": 159}, {"name": "@case-manager", "line": 159}], "type": "scenario"}], "id": "case-manager", "line": 1, "keyword": "Feature", "name": "Case Manager", "tags": [], "uri": "cypress\\e2e\\features\\case-manager.feature"}]