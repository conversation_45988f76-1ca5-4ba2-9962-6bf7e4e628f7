import {
  Before,
  Given,
  When,
  Then,
} from '@badeball/cypress-cucumber-preprocessor';
import { caseManagerPage } from '../../../pages/caseManagerPage';

const testFolders = {
  folderA1: 'folderA1',
  folderA2: 'folderA2',
  folderA3: 'folderA3',
  folderB: 'folderB',
};

const users = {
  userA: 'user1',
  userB: 'user2',
  userC: 'user3',
  admin: 'user1',
};

Before({ tags: '@multi-user' }, () => {
  cy.LoginAndSwitchOrg(
    '71459e59-e555-4c3d-a391-9adb79a6576b',
    Cypress.env('username')
  );
});

Given('The test environment is cleaned up', () => {
  cy.loginAsUser(users.admin);
  cy.visit('/');

  Object.values(testFolders).forEach((folderName) => {
    caseManagerPage.deleteFolderAndSDOsIfExist(folderName);
  });
});

When('User<PERSON> creates three folders', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');

  [testFolders.folderA1, testFolders.folderA2, testFolders.folderA3].forEach(
    (folderName) => {
      caseManagerPage.createDefaultCase(folderName);
      cy.contains('Case Created Successfully');
      caseManagerPage.closeSuccessModal();
    }
  );
});

When('UserB creates one folder', () => {
  cy.loginAsUser(users.userB);
  cy.visit('/');

  caseManagerPage.createDefaultCase(testFolders.folderB);
  cy.contains('Case Created Successfully');
  caseManagerPage.closeSuccessModal();
});

When('UserA shares folderA2 with UserB', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');

  caseManagerPage.getCaseRowByCaseId(testFolders.folderA2).should('be.visible');
  cy.log('Folder sharing functionality would be implemented here');
});

When('UserA shares folderA3 with all users', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');

  caseManagerPage.getCaseRowByCaseId(testFolders.folderA3).should('be.visible');
  cy.log('Folder sharing with all users would be implemented here');
});

When('UserB logs in and creates folderB', () => {
  cy.loginAsUser(users.userB);
  cy.visit('/');

  caseManagerPage.createDefaultCase(testFolders.folderB);
  cy.contains('Case Created Successfully');
  caseManagerPage.closeSuccessModal();
});

When('UserB shares folderB with UserC', () => {
  cy.loginAsUser(users.userB);
  cy.visit('/');

  caseManagerPage.getCaseRowByCaseId(testFolders.folderB).should('be.visible');
  cy.log('Folder sharing with UserC would be implemented here');
});

Then('UserA can see their own folders', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');

  caseManagerPage.verifyCaseInTable(testFolders.folderA1);
  caseManagerPage.verifyCaseInTable(testFolders.folderA2);
  caseManagerPage.verifyCaseInTable(testFolders.folderA3);
});

Then('UserB can see their own folder', () => {
  cy.loginAsUser(users.userB);
  cy.visit('/');

  caseManagerPage.verifyCaseInTable(testFolders.folderB);
});

Then('UserC has limited access', () => {
  cy.loginAsUser(users.userC);
  cy.visit('/');

  cy.log('UserC should have limited access - checking main page loads');
  cy.get('body').should('contain', 'Case Management');
});

Then('UserA can access their own folders and shared folders', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');

  caseManagerPage.verifyCaseInTable(testFolders.folderA1);
  caseManagerPage.verifyCaseInTable(testFolders.folderA2);
  caseManagerPage.verifyCaseInTable(testFolders.folderA3);
});

Then('UserB can access shared folders from UserA and their own folder', () => {
  cy.loginAsUser(users.userB);
  cy.visit('/');

  caseManagerPage.verifyCaseInTable(testFolders.folderA2);
  caseManagerPage.verifyCaseInTable(testFolders.folderA3);
  caseManagerPage.verifyCaseInTable(testFolders.folderB);
});

Then('UserC can access shared folders only', () => {
  cy.loginAsUser(users.userC);
  cy.visit('/');

  caseManagerPage.verifyCaseInTable(testFolders.folderA3);
  caseManagerPage.verifyCaseInTable(testFolders.folderB);
});

Then('Admin user can access all folders', () => {
  cy.loginAsUser(users.admin);
  cy.visit('/');

  Object.values(testFolders).forEach((folderName) => {
    caseManagerPage.verifyCaseInTable(folderName);
  });
});

Then('The test cleanup removes all created folders', () => {
  cy.loginAsUser(users.admin);
  cy.visit('/');

  Object.values(testFolders).forEach((folderName) => {
    caseManagerPage.deleteFolderAndSDOsIfExist(folderName);
  });
});

Given('The test folders exist', () => {
  cy.loginAsUser(users.admin);
  cy.visit('/');

  Object.values(testFolders).forEach((folderName) => {
    caseManagerPage.deleteFolderAndSDOsIfExist(folderName);
  });

  Object.values(testFolders).forEach((folderName) => {
    caseManagerPage.createDefaultCase(folderName);
    cy.contains('Case Created Successfully');
    caseManagerPage.closeSuccessModal();
  });
});

When('UserA logs in', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');
});

When('UserB logs in', () => {
  cy.loginAsUser(users.userB);
  cy.visit('/');
});

When('UserC logs in', () => {
  cy.loginAsUser(users.userC);
  cy.visit('/');
});

Then('UserA can see folderA1, folderA2, folderA3', () => {
  caseManagerPage.verifyCaseInTable(testFolders.folderA1);
  caseManagerPage.verifyCaseInTable(testFolders.folderA2);
  caseManagerPage.verifyCaseInTable(testFolders.folderA3);
});

Then('UserA cannot see folderB', () => {
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderB);
});

Then('UserB can see folderA2, folderA3, folderB', () => {
  caseManagerPage.verifyCaseInTable(testFolders.folderA2);
  caseManagerPage.verifyCaseInTable(testFolders.folderA3);
  caseManagerPage.verifyCaseInTable(testFolders.folderB);
});

Then('UserB cannot see folderA1', () => {
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderA1);
});

Then('UserC can see folderA3, folderB only', () => {
  caseManagerPage.verifyCaseInTable(testFolders.folderA3);
  caseManagerPage.verifyCaseInTable(testFolders.folderB);
});

Then('UserC cannot see folderA1, folderA2', () => {
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderA1);
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderA2);
});
