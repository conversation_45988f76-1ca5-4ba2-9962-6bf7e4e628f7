import {
  Before,
  Given,
  When,
  Then,
} from '@badeball/cypress-cucumber-preprocessor';
import { caseManagerPage } from '../../../pages/caseManagerPage';

const testFolders = {
  folderA1: 'folderA1',
  folderA2: 'folderA2',
  folderA3: 'folderA3',
  folderC: 'folderC',
};

const users = {
  userA: 'user1', // <EMAIL> - Can create folders
  userB: 'user2', // <EMAIL> - View-only, no create permissions
  userC: 'user3', // <EMAIL> - Can create folders
  admin: 'user1',
};

Before({ tags: '@multi-user' }, () => {
  cy.LoginAndSwitchOrg(
    '71459e59-e555-4c3d-a391-9adb79a6576b',
    Cypress.env('username')
  );
});

Given('The test environment is cleaned up', () => {
  cy.loginAsUser(users.admin);
  cy.visit('/');

  Object.values(testFolders).forEach((folderName) => {
    caseManagerPage.deleteFolderAndSDOsIfExist(folderName);
  });
});

When('UserA creates three folders', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');

  [testFolders.folderA1, testFolders.folderA2, testFolders.folderA3].forEach(
    (folderName) => {
      caseManagerPage.createDefaultCase(folderName);
      cy.contains('Case Created Successfully');
      caseManagerPage.closeSuccessModal();
    }
  );
});

When('UserC creates one folder', () => {
  cy.loginAsUser(users.userC);
  cy.visit('/');

  caseManagerPage.createDefaultCase(testFolders.folderC);
  cy.contains('Case Created Successfully');
  caseManagerPage.closeSuccessModal();
});

When('UserA shares folderA2 with UserB', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');

  caseManagerPage.getCaseRowByCaseId(testFolders.folderA2).should('be.visible');
  cy.log('Folder sharing functionality would be implemented here');
});

When('UserA shares folderA3 with all users', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');

  caseManagerPage.getCaseRowByCaseId(testFolders.folderA3).should('be.visible');
  cy.log('Folder sharing with all users would be implemented here');
});

Then('UserA can see their own folders', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');

  caseManagerPage.verifyCaseInTable(testFolders.folderA1);
  caseManagerPage.verifyCaseInTable(testFolders.folderA2);
  caseManagerPage.verifyCaseInTable(testFolders.folderA3);
});

Then('UserC can see their own folder', () => {
  cy.loginAsUser(users.userC);
  cy.visit('/');

  caseManagerPage.verifyCaseInTable(testFolders.folderC);
});

Then('UserB has view-only access', () => {
  cy.loginAsUser(users.userB);
  cy.visit('/');

  cy.log('UserB has view-only access - checking main page loads');
  cy.get('body').should('contain', 'Case Management');

  cy.getDataIdCy({ idAlias: 'AddIcon' }).should('not.exist');
});

Then('Admin user can access all folders', () => {
  cy.loginAsUser(users.admin);
  cy.visit('/');

  Object.values(testFolders).forEach((folderName) => {
    caseManagerPage.verifyCaseInTable(folderName);
  });
});

Then('The test cleanup removes all created folders', () => {
  cy.loginAsUser(users.admin);
  cy.visit('/');

  Object.values(testFolders).forEach((folderName) => {
    caseManagerPage.deleteFolderAndSDOsIfExist(folderName);
  });
});

Given('The test folders exist', () => {
  cy.loginAsUser(users.admin);
  cy.visit('/');

  Object.values(testFolders).forEach((folderName) => {
    caseManagerPage.deleteFolderAndSDOsIfExist(folderName);
  });

  Object.values(testFolders).forEach((folderName) => {
    caseManagerPage.createDefaultCase(folderName);
    cy.contains('Case Created Successfully');
    caseManagerPage.closeSuccessModal();
  });
});

When('UserA logs in', () => {
  cy.loginAsUser(users.userA);
  cy.visit('/');
});

When('UserB logs in', () => {
  cy.loginAsUser(users.userB);
  cy.visit('/');
});

When('UserC logs in', () => {
  cy.loginAsUser(users.userC);
  cy.visit('/');
});

Then('UserA can see folderA1, folderA2, folderA3', () => {
  caseManagerPage.verifyCaseInTable(testFolders.folderA1);
  caseManagerPage.verifyCaseInTable(testFolders.folderA2);
  caseManagerPage.verifyCaseInTable(testFolders.folderA3);
});

Then('UserA cannot see folderC', () => {
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderC);
});

Then('UserC can see folderC only', () => {
  cy.loginAsUser(users.userC);
  cy.visit('/');

  caseManagerPage.verifyCaseInTable(testFolders.folderC);
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderA1);
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderA2);
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderA3);
});

Then('UserC cannot see folderA1, folderA2, folderA3', () => {
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderA1);
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderA2);
  caseManagerPage.verifyCaseRemovedFromTable(testFolders.folderA3);
});

Then('UserB cannot create folders', () => {
  cy.loginAsUser(users.userB);
  cy.visit('/');

  cy.getDataIdCy({ idAlias: 'AddIcon' }).should('not.exist');
});

Then('UserB has view-only access to the system', () => {
  cy.loginAsUser(users.userB);
  cy.visit('/');

  cy.log('UserB has view-only access - checking main page loads');
  cy.get('body').should('contain', 'Case Management');

  cy.getDataIdCy({ idAlias: 'AddIcon' }).should('not.exist');
});
