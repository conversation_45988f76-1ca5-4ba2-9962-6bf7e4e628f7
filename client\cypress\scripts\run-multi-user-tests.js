const { execSync } = require('child_process');

const runMultiUserTests = () => {
  console.log('🚀 Starting Multi-User Folder Permissions Tests...');
  
  try {
    const command = 'npx cypress run --spec "cypress/e2e/features/multi-user-folder-permissions.feature" --browser chrome';
    
    console.log('📋 Running command:', command);
    
    const result = execSync(command, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log('✅ Multi-user tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Multi-user tests failed:', error.message);
    process.exit(1);
  }
};

const runHeadlessTests = () => {
  console.log('🚀 Starting Multi-User Tests in Headless Mode...');
  
  try {
    const command = 'npx cypress run --spec "cypress/e2e/features/multi-user-folder-permissions.feature" --headless';
    
    console.log('📋 Running command:', command);
    
    const result = execSync(command, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
    console.log('✅ Headless multi-user tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Headless multi-user tests failed:', error.message);
    process.exit(1);
  }
};

const openCypress = () => {
  console.log('🚀 Opening Cypress Test Runner...');
  
  try {
    const command = 'npx cypress open --e2e';
    
    console.log('📋 Running command:', command);
    
    execSync(command, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    
  } catch (error) {
    console.error('❌ Failed to open Cypress:', error.message);
    process.exit(1);
  }
};

const args = process.argv.slice(2);
const mode = args[0] || 'run';

switch (mode) {
  case 'run':
    runMultiUserTests();
    break;
  case 'headless':
    runHeadlessTests();
    break;
  case 'open':
    openCypress();
    break;
  default:
    console.log('Usage: node run-multi-user-tests.js [run|headless|open]');
    console.log('  run      - Run tests in Chrome browser');
    console.log('  headless - Run tests in headless mode');
    console.log('  open     - Open Cypress test runner');
    break;
}
