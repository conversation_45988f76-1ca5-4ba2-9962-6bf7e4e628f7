# Multi-User Testing Guide

This guide explains how to use the multi-user folder permissions testing framework for the Investigate application.

## Overview

The multi-user testing framework allows you to test folder sharing and permissions across different user accounts. It simulates real-world scenarios where:

- UserA creates folders and shares them with other users
- UserB has limited access to shared folders
- <PERSON>rC (<EMAIL>) has view-only access
- Admin users can access all folders

## Test Accounts Configuration

The tests use 3 accounts from your `cypress.env.json`:

```json
{
  "users": {
    "user1": {
      "username": "<EMAIL>",
      "password": "your-password"
    },
    "user2": {
      "username": "<EMAIL>", 
      "password": "your-password"
    },
    "user3": {
      "username": "<EMAIL>",
      "password": "your-password"
    }
  }
}
```

## User Roles in Tests

- **UserA (user1)**: Primary admin user - creates folders, shares with others
- **UserB (user2)**: Limited access user - can view shared folders
- **UserC (user3)**: Restricted user - minimal permissions

## Test Scenarios

### Basic Multi-User Test
Tests basic folder creation and access patterns:
- UserA creates 3 folders (folderA1, folderA2, folderA3)
- UserB creates 1 folder (folderB)
- Each user can see their own folders
- UserC has limited access

### Advanced Sharing Test (Skipped by default)
Tests folder sharing functionality:
- UserA shares folderA2 with UserB
- UserA shares folderA3 with all users
- UserB shares folderB with UserC
- Verifies proper access controls

## Running the Tests

### Option 1: Using the Test Runner Script
```bash
cd client
node cypress/scripts/run-multi-user-tests.js run      # Run in Chrome
node cypress/scripts/run-multi-user-tests.js headless # Run headless
node cypress/scripts/run-multi-user-tests.js open     # Open Cypress UI
```

### Option 2: Direct Cypress Commands
```bash
cd client
npx cypress run --spec "cypress/e2e/features/multi-user-folder-permissions.feature"
npx cypress open --e2e
```

### Option 3: Run Specific Tags
```bash
npx cypress run --env grepTags="@multi-user"
npx cypress run --env grepTags="@folder-permissions"
```

## Test Structure

### Feature File
`cypress/e2e/features/multi-user-folder-permissions.feature`
- Contains Gherkin scenarios for multi-user testing
- Uses tags for test organization (@multi-user, @folder-permissions)

### Step Definitions
`cypress/e2e/step_definitions/multi-user-folder-permissions/multi-user-folder-permissions.steps.ts`
- Implements the test steps
- Handles user switching and folder operations
- Uses existing page objects and helpers

### Custom Commands
Added to `cypress/support/commands.ts`:
- `cy.loginAsUser(userKey)` - Switch between test users
- `cy.shareFolderWithUser(folderId, userEmail, permissionLevel)` - Share folders
- `cy.getFolderPermissions(folderId)` - Check folder permissions

## Key Features

### User Switching
```typescript
cy.loginAsUser('user1'); // Login as UserA
cy.loginAsUser('user2'); // Login as UserB
cy.loginAsUser('user3'); // Login as UserC
```

### Folder Operations
```typescript
caseManagerPage.createDefaultCase('folderName');
caseManagerPage.verifyCaseInTable('folderName');
caseManagerPage.verifyCaseRemovedFromTable('folderName');
```

### Cleanup
```typescript
caseManagerPage.deleteFolderAndSDOsIfExist(undefined, {
  hashes: () => Object.values(testFolders).map(name => ({ folderName: name }))
});
```

## Troubleshooting

### Common Issues

1. **Authentication Failures**
   - Verify passwords in cypress.env.json
   - Check organization GUID in step definitions

2. **Folder Not Found**
   - Ensure cleanup runs before tests
   - Check folder naming consistency

3. **Permission Errors**
   - Verify user has proper permissions in the organization
   - Check permission set IDs in sharing commands

### Debug Mode
Add `cy.log()` statements to track test execution:
```typescript
cy.log('Creating folder for UserA');
cy.log('Switching to UserB');
```

## Extending the Tests

### Adding New Users
1. Add user credentials to cypress.env.json
2. Update the users object in step definitions
3. Add new test scenarios

### Adding New Scenarios
1. Add Gherkin scenarios to the feature file
2. Implement corresponding step definitions
3. Use existing page objects and helpers

### Custom Assertions
```typescript
Then('User can access specific folder', () => {
  cy.loginAsUser('user1');
  cy.visit('/');
  caseManagerPage.verifyCaseInTable('specificFolder');
});
```

## Best Practices

1. **Always clean up** - Remove test data after each scenario
2. **Use descriptive names** - Make folder names unique and identifiable
3. **Handle async operations** - Wait for API responses and UI updates
4. **Isolate tests** - Each test should be independent
5. **Use existing patterns** - Follow the established codebase conventions

## Integration with CI/CD

Add to your pipeline:
```yaml
- name: Run Multi-User Tests
  run: |
    cd client
    node cypress/scripts/run-multi-user-tests.js headless
```
