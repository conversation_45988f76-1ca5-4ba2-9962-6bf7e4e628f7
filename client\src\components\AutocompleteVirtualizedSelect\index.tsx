import {
  useRef,
  useState,
  useEffect,
  useCallback,
  useLayoutEffect,
} from 'react';
import {
  FormControl,
  MenuItem,
  Popper,
  TextField,
  Paper,
  CircularProgress,
} from '@mui/material';
import { I18nTranslate } from '@i18n';
import { FixedSizeList, ListChildComponentProps } from 'react-window';
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { ApiStatus } from '@store/types';

const ITEM_SIZE = 48;
const LIST_MAX_VISIBLE = 8;

export interface Option {
  id: string;
  label: string;
}

interface AutocompleteVirtualizedSelectProps {
  options: Option[];
  value: string | undefined;
  onChange: (value: string) => void;
  loadMoreItems: () => void;
  hasMore: boolean;
  loading: ApiStatus;
  placeholder: string;
}

export default function AutocompleteVirtualizedSelect({
  options,
  value,
  onChange,
  loadMoreItems,
  hasMore = false,
  loading,
  placeholder,
}: AutocompleteVirtualizedSelectProps) {
  const intl = I18nTranslate.Intl();
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const containerRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<FixedSizeList>(null);
  const listOuterRef = useRef<HTMLDivElement>(null);
  const [highlightedIndex, setHighlightedIndex] = useState<number>(-1);
  const [dropdownWidth, setDropdownWidth] = useState<number>(0);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter options by inputValue
  const filteredOptions = inputValue
    ? options.filter((opt) =>
        opt.label.toLowerCase().includes(inputValue.toLowerCase())
      )
    : options;
  const itemCount = hasMore
    ? filteredOptions.length + 1
    : filteredOptions.length;

  // Handle infinite scroll
  const handleItemsRendered = useCallback(
    ({ visibleStopIndex }: { visibleStopIndex: number }) => {
      if (
        hasMore &&
        loadMoreItems &&
        visibleStopIndex >= filteredOptions.length - 1
      ) {
        loadMoreItems();
      }
    },
    [hasMore, loadMoreItems, filteredOptions.length]
  );

  useEffect(() => {
    // If dropdown is closed and value changes, show the selected label in the input
    if (!open) {
      const selected = options.find((opt) => opt.id === value);
      setInputValue(selected ? selected.label : '');
    }
  }, [value]);

  // Keyboard navigation
  useEffect(() => {
    if (!open) {
      return;
    }
    const handler = (e: KeyboardEvent) => {
      if (e.key === 'ArrowDown') {
        setHighlightedIndex((i) => Math.min(i + 1, itemCount - 1));
        e.preventDefault();
        e.stopPropagation();
      } else if (e.key === 'ArrowUp') {
        setHighlightedIndex((i) => Math.max(i - 1, 0));
        e.preventDefault();
        e.stopPropagation();
      } else if (
        e.key === 'Enter' &&
        highlightedIndex >= 0 &&
        highlightedIndex < filteredOptions.length
      ) {
        onChange(filteredOptions[highlightedIndex].id);
        setOpen(false);
        e.preventDefault();
        e.stopPropagation();
      } else if (e.key === 'Escape') {
        setOpen(false);
        e.preventDefault();
        e.stopPropagation();
      }
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, [open, highlightedIndex, filteredOptions, itemCount, onChange]);

  // After each load, check if the list is scrollable. If not, and hasMore, load more.
  useLayoutEffect(() => {
    if (!open || !hasMore || !loadMoreItems) {
      return;
    }
    const outer = listOuterRef.current;
    if (
      outer &&
      outer.scrollHeight <= outer.clientHeight &&
      loading !== 'loading' &&
      loading !== 'idle'
    ) {
      loadMoreItems();
    }
  }, [filteredOptions.length, hasMore, open, loading]);

  // Set dropdown width based on container width
  useEffect(() => {
    if (open && containerRef.current) {
      setDropdownWidth(containerRef.current.offsetWidth);
    }
  }, [open]);

  // Close menuItem popper on escape
  useEffect(() => {
    const input = inputRef.current;
    if (!input) {
      return;
    }
    const handler = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && open) {
        setOpen(false);
        e.preventDefault();
        e.stopPropagation();
      }
    };
    input.addEventListener('keydown', handler);
    return () => input.removeEventListener('keydown', handler);
  }, [open]);

  // Render each row
  const renderRow = ({ index, style }: ListChildComponentProps) => {
    if (index === filteredOptions.length) {
      return (
        <MenuItem
          style={{ ...style, width: dropdownWidth }}
          disabled
          key="loading-more"
        >
          {loading === 'loading' ? (
            <CircularProgress size={20} />
          ) : (
            intl.formatMessage({ id: 'loading', defaultMessage: 'Loading...' })
          )}
        </MenuItem>
      );
    }
    const option = filteredOptions[index];
    return (
      <MenuItem
        style={{ ...style, width: dropdownWidth }}
        key={option.id}
        selected={index === highlightedIndex}
        onClick={() => {
          onChange(option.id);
          setOpen(false);
        }}
        onMouseEnter={() => setHighlightedIndex(index)}
      >
        {option.label}
      </MenuItem>
    );
  };

  return (
    <FormControl fullWidth>
      <div ref={containerRef} style={{ width: '100%' }}>
        <TextField
          fullWidth
          placeholder={placeholder}
          value={inputValue}
          inputRef={inputRef}
          onBlur={() => setTimeout(() => setOpen(false), 150)}
          onChange={(e) => {
            if (e.target.value === '') {
              setOpen(false);
            } else {
              setOpen(true);
            }
            setInputValue(e.target.value);
          }}
          slotProps={{
            input: {
              endAdornment: (
                <>
                  {loading === 'loading' ? (
                    <CircularProgress size={20} />
                  ) : null}
                  <ArrowDropDownIcon
                    style={{ cursor: 'pointer', marginLeft: 4 }}
                    onMouseDown={(e) => {
                      e.preventDefault();
                      setOpen((o) => !o);
                      inputRef.current?.focus();
                    }}
                  />
                </>
              ),
            },
          }}
        />
      </div>
      <Popper
        open={open}
        anchorEl={containerRef.current}
        placement="bottom-start"
        style={{ zIndex: 1300, width: dropdownWidth }}
      >
        <Paper
          elevation={3}
          style={{
            marginTop: 4,
            maxHeight: ITEM_SIZE * LIST_MAX_VISIBLE,
            width: dropdownWidth,
          }}
        >
          <FixedSizeList
            ref={listRef}
            outerRef={listOuterRef}
            height={Math.min(LIST_MAX_VISIBLE, itemCount) * ITEM_SIZE}
            width={dropdownWidth}
            itemSize={ITEM_SIZE}
            itemCount={itemCount}
            onItemsRendered={({ visibleStopIndex }) =>
              handleItemsRendered({ visibleStopIndex })
            }
            overscanCount={5}
          >
            {renderRow}
          </FixedSizeList>
        </Paper>
      </Popper>
    </FormControl>
  );
}
