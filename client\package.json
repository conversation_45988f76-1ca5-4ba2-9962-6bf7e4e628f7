{"name": "investigate-client", "private": true, "version": "0.0.0", "type": "module", "scripts": {"start:dev": "vite", "build": "vite build --mode development", "lint": "tsc --noEmit --project tsconfig.lint.json && eslint --max-warnings 0", "lint:fix": "eslint --fix ./src", "lint:cypress": "eslint \"./cypress/**/*.{ts,tsx}\" --max-warnings 0", "lint:cypress:fix": "eslint --fix ./cypress", "preview": "vite preview", "test": "vitest", "test:run": "vitest --run", "test:coverage": "vitest run --coverage", "format": "prettier --write \"./**/*.{ts,tsx,js,css,scss,md,json}\"", "outdated": "yarn upgrade-interactive", "depcheck": "depcheck --ignores=\"@vitest/coverage-v8, eslint-plugin-testing-library\"", "cy:runStage": "cypress run --env ENVIRONMENT=stage", "cy:openLocal": "cypress open --env ENVIRONMENT=local", "cy:runLocal": "cypress run --env ENVIRONMENT=local", "cy:runLocal:exclude-health": "yarn cy:runLocal --spec=$(find cypress/e2e/features -name \"*.feature\" ! -name \"health-check.feature\" | paste -sd \",\" -)", "cy:multi-user": "node cypress/scripts/run-multi-user-tests.js run", "cy:multi-user:headless": "node cypress/scripts/run-multi-user-tests.js headless", "cy:multi-user:open": "node cypress/scripts/run-multi-user-tests.js open", "prepare": "cd ../ && yarn"}, "dependencies": {"@badeball/cypress-cucumber-preprocessor": "^22.0.1", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@eslint/plugin-kit": "^0.3.5", "@fontsource/dosis": "5.1.1", "@fontsource/nunito": "^5.1.1", "@mui/icons-material": "^6.4.3", "@mui/lab": "6.0.0-beta.24", "@mui/material": "^6.4.3", "@mui/system": "6.4.1", "@mui/utils": "^6.4.1", "@mui/x-date-pickers": "^7.26.0", "@reduxjs/toolkit": "^2.5.1", "@sentry/browser": "^9.0.1", "@tanstack/react-virtual": "^3.13.10", "@veritone/glc-redux": "^1.0.12", "axios": "1.8.3", "classnames": "2.5.1", "date-fns": "^4.1.0", "form-data": "^4.0.4", "lodash": "^4.17.21", "luxon": "^3.6.1", "material-react-table": "^3.1.0", "mime-db": "^1.54.0", "notistack": "^3.0.2", "p-iteration": "^1.1.8", "react": "^19.0.0", "react-beautiful-dnd": "13.1.1", "react-colorful": "^5.6.1", "react-date-range": "^2.0.1", "react-dom": "^19.0.0", "react-hook-form": "7.54.2", "react-markdown": "9.0.3", "react-redux": "9.2.0", "react-router": "^7.6.0", "react-virtualized-auto-sizer": "1.0.25", "react-virtuoso": "^4.12.5", "react-window": "1.8.11", "react-window-infinite-loader": "1.0.10", "redux-api-middleware": "^3.2.1", "redux-dynamic-modules-core": "5.2.3", "redux-dynamic-modules-saga": "5.2.3", "redux-logger": "^3.0.6", "redux-saga": "^1.3.0", "rsuite": "^5.77.0", "sass": "^1.84.0", "tmp": "^0.2.5", "ts-essentials": "^10.0.4", "tss-react": "4.9.15", "undici": "^7.10.0", "uuid": "11.0.5", "zod": "^4.0.5"}, "devDependencies": {"@cspell/eslint-plugin": "^9.0.2", "@cypress/webpack-preprocessor": "^6.0.2", "@eslint/js": "^9.22.0", "@mui/styles": "6.4.1", "@storybook/react": "8.5.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "16.2.0", "@testing-library/user-event": "^13.1.9", "@types/lodash": "4.17.17", "@types/luxon": "3.6.2", "@types/mime-db": "^1.43.6", "@types/mocha": "^10.0.10", "@types/node": "^22.15.31", "@types/react": "^19.0.11", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-date-range": "^1.4.10", "@types/react-dom": "^19.0.4", "@types/react-virtualized-auto-sizer": "1.0.4", "@types/react-window": "1.8.8", "@types/react-window-infinite-loader": "1.0.9", "@types/redux-api-middleware": "^3.2.7", "@types/redux-logger": "^3.0.13", "@types/tmp": "^0", "@types/uuid": "10.0.0", "@vitejs/plugin-react-swc": "^3.9.0", "@vitest/coverage-v8": "3.1.4", "cypress": "^14.0.3", "cypress-file-upload": "^5.0.8", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-import-resolver-custom-alias": "^1.3.2", "eslint-import-resolver-typescript": "^4.4.3", "eslint-plugin-cypress": "^5.1.0", "eslint-plugin-formatjs": "^5.3.1", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.4.1", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-react": "7.37.5", "eslint-plugin-testing-library": "^7.1.1", "git-commit-info": "^2.0.2", "globals": "^16.2.0", "jiti": "^2.4.2", "jsdom": "^26.0.0", "prettier": "^3.5.3", "react-intl": "7.1.11", "storybook": "8.6.7", "stylus": "0.64.0", "ts-loader": "^9.5.2", "typed-redux-saga": "^1.5.0", "typescript": "5.8.3", "typescript-eslint": "8.34.0", "vite": "^6.3.5", "vite-plugin-csp": "^1.1.2", "vite-plugin-mkcert": "^1.17.8", "vite-plugin-svgr": "^4.3.0", "vitest": "^3.1.4", "webpack": "^5.99.9"}, "resolutions": {"@babel/helpers": "^7.26.10", "@babel/runtime": "^7.26.10", "is-git-repository/execa": "^4.0.3"}, "cypress-cucumber-preprocessor": {"stepDefinitions": "cypress/e2e/**/[filepath]/*.{js,ts}", "json": {"enabled": true, "output": "cypress/reports/cucumber.json"}}, "packageManager": "yarn@4.9.1"}